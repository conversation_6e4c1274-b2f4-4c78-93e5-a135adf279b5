<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lysjk.mapper.TWaterSpectrumMapper">

  <!-- 基础结果映射 -->
  <resultMap id="BaseResultMap" type="com.lysjk.entity.TWaterSpectrum">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="point_id" jdbcType="INTEGER" property="pointId" />
    <result column="region_id" jdbcType="INTEGER" property="regionId" />
    <result column="monitoring_dt" jdbcType="TIMESTAMP" property="monitoringDt" />
    <result column="spectrum" jdbcType="OTHER" property="spectrum" typeHandler="com.lysjk.config.typehandler.JsonMapTypeHandler" />
    <result column="create_dt" jdbcType="TIMESTAMP" property="createDt" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="update_dt" jdbcType="TIMESTAMP" property="updateDt" />
    <result column="update_by" jdbcType="INTEGER" property="updateBy" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>

  <!-- 基础字段列表 -->
  <sql id="Base_Column_List">
    id, point_id, region_id, monitoring_dt, spectrum,
    create_dt, create_by, update_dt, update_by, remark
  </sql>

  <!-- 新增波谱信息 -->
  <insert id="insert" useGeneratedKeys="true" keyProperty="id">
    insert into t_water_spectrum
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pointId != null">point_id,</if>
      <if test="regionId != null">region_id,</if>
      <if test="monitoringDt != null">monitoring_dt,</if>
      <if test="spectrum != null">spectrum,</if>
      <if test="createDt != null">create_dt,</if>
      <if test="createBy != null">create_by,</if>
      <if test="updateDt != null">update_dt,</if>
      <if test="updateBy != null">update_by,</if>
      <if test="remark != null and remark != ''">remark,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pointId != null">#{pointId},</if>
      <if test="regionId != null">#{regionId},</if>
      <if test="monitoringDt != null">#{monitoringDt},</if>
      <if test="spectrum != null">#{spectrum,typeHandler=com.lysjk.config.typehandler.JsonMapTypeHandler},</if>
      <if test="createDt != null">#{createDt},</if>
      <if test="createBy != null">#{createBy},</if>
      <if test="updateDt != null">#{updateDt},</if>
      <if test="updateBy != null">#{updateBy},</if>
      <if test="remark != null and remark != ''">#{remark},</if>
    </trim>
  </insert>

  <!-- 批量删除波谱信息 -->
  <delete id="deleteByIds">
    delete from t_water_spectrum where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <!-- 更新波谱信息 -->
  <update id="update">
    update t_water_spectrum
    <set>
      <if test="pointId != null">point_id = #{pointId},</if>
      <if test="regionId != null">region_id = #{regionId},</if>
      <if test="monitoringDt != null">monitoring_dt = #{monitoringDt},</if>
      <if test="spectrum != null">spectrum = #{spectrum,typeHandler=com.lysjk.config.typehandler.JsonMapTypeHandler},</if>
      <if test="updateDt != null">update_dt = #{updateDt},</if>
      <if test="updateBy != null">update_by = #{updateBy},</if>
      <if test="remark != null and remark != ''">remark = #{remark},</if>
    </set>
    where id = #{id}
  </update>

  <!-- 分页查询波谱信息 -->
  <select id="pageQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_water_spectrum
    <where>
      <if test="pointId != null">
        and point_id = #{pointId}
      </if>
      <if test="regionId != null">
        and region_id = #{regionId}
      </if>
      <if test="monitoringDt != null">
        and monitoring_dt = #{monitoringDt}
      </if>
    </where>
    order by monitoring_dt desc, create_dt desc
  </select>

  <!-- 批量新增波谱信息 -->
  <insert id="batchInsert">
    insert into t_water_spectrum (point_id, region_id, monitoring_dt, spectrum,
      create_dt, create_by, update_dt, update_by, remark)
    values
    <foreach collection="waterSpectrumList" item="item" separator=",">
      (#{item.pointId,jdbcType=INTEGER}, #{item.regionId,jdbcType=INTEGER}, #{item.monitoringDt,jdbcType=TIMESTAMP},
       #{item.spectrum,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.JsonMapTypeHandler},
       #{item.createDt,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=INTEGER},
       #{item.updateDt,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=INTEGER},
       #{item.remark,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <!-- 根据地物ID列表查询波谱信息 -->
  <select id="selectByRegionIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_water_spectrum
    where region_id in
    <foreach collection="regionIds" item="regionId" open="(" close=")" separator=",">
      #{regionId}
    </foreach>
    order by monitoring_dt desc, create_dt desc
  </select>

  <!-- 查询所有波谱信息 -->
  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_water_spectrum
    order by monitoring_dt desc, create_dt desc
  </select>

</mapper>
