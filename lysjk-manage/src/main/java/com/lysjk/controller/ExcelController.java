package com.lysjk.controller;

import com.lysjk.result.Result;
import com.lysjk.service.ExcelParseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * Excel解析控制器
 * 用于处理Excel文件的上传和解析，将Excel数据录入到数据库中
 */
@RestController
@RequestMapping("/excel")
@Slf4j
public class ExcelController {

    @Autowired
    private ExcelParseService excelParseService;

    /**
     * 解析Excel文件并录入数据库
     *
     * @param file Excel文件
     * @return 解析结果
     */
    @PostMapping("/parse")
    public Result<String> parseExcel(@RequestParam("file") MultipartFile file) {

        log.info("开始解析Excel文件: {}, 大小: {} bytes",
            file.getOriginalFilename(), file.getSize());

        try {
            // 文件基本验证
            if (file.isEmpty()) {
                log.warn("上传的Excel文件为空");
                return Result.error("上传的文件为空");
            }

            // 文件类型验证
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null ||
                (!originalFilename.toLowerCase().endsWith(".xlsx") &&
                 !originalFilename.toLowerCase().endsWith(".xls"))) {
                log.warn("上传的文件不是Excel格式: {}", originalFilename);
                return Result.error("请上传Excel格式文件(.xlsx或.xls)");
            }

            // 文件大小验证（限制为50MB）
            if (file.getSize() > 50 * 1024 * 1024) {
                log.warn("上传的Excel文件过大: {} bytes", file.getSize());
                return Result.error("文件大小不能超过50MB");
            }

            // 调用服务解析Excel文件
            String result = excelParseService.parseAndSaveExcel(file);

            log.info("Excel文件解析完成: {}", result);
            return Result.success(result);

        } catch (Exception e) {
            return Result.error("解析Excel文件失败: " + e.getMessage());
        }
    }
}
