package com.lysjk.controller;

import com.lysjk.service.ExcelExportService;
import com.lysjk.service.impl.ExcelExportServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Excel导出控制器
 * 提供水质监测数据的Excel导出功能
 */
@Slf4j
@RestController
@RequestMapping("/excel/export")
public class ExcelExportController {

    public static final String FILE_NAME = "流域数据完整表";

    @Autowired
    private ExcelExportService excelExportService;

    private static final DateTimeFormatter FILENAME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    /**
     * 导出完整的Excel文件
     * 包含所有7个工作表：主表、监测点信息表、环境参数表、水质参数表、反射率光谱表、测量记录表、影像信息表
     *
     * @param regionIds 地物ID列表，可选参数，如果不提供则导出所有数据
     * @return Excel文件
     */
    @GetMapping("/complete")
    public ResponseEntity<Resource> exportCompleteExcel(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出完整Excel文件，地物ID列表: {}", regionIds);

        try {
            Resource resource = excelExportService.exportCompleteExcel(regionIds);

            String contentDisposition = generateFilename(FILE_NAME);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition)
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"))
                    .body(resource);

        } catch (Exception e) {
            log.error("导出完整Excel文件失败", e);
            throw new RuntimeException("导出Excel文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出地物信息主表
     *
     * @param regionIds 地物ID列表，可选参数
     * @return Excel文件
     */
    @GetMapping("/region-info")
    public ResponseEntity<Resource> exportRegionInfoMain(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出地物信息主表，地物ID列表: {}", regionIds);

        try {
            Resource resource = excelExportService.exportRegionInfoMain(regionIds);

            String contentDisposition = generateFilename(ExcelExportServiceImpl.SHEET_REGION_INFO);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition)
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"))
                    .body(resource);

        } catch (Exception e) {
            log.error("导出地物信息主表失败", e);
            throw new RuntimeException("导出地物信息主表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出监测点信息表
     *
     * @param regionIds 地物ID列表，可选参数
     * @return Excel文件
     */
    @GetMapping("/monitoring-point")
    public ResponseEntity<Resource> exportMonitoringPoint(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出监测点信息表，地物ID列表: {}", regionIds);

        try {
            Resource resource = excelExportService.exportMonitoringPoint(regionIds);

            String contentDisposition = generateFilename(ExcelExportServiceImpl.SHEET_MONITORING_POINT);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition)
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"))
                    .body(resource);

        } catch (Exception e) {
            log.error("导出监测点信息表失败", e);
            throw new RuntimeException("导出监测点信息表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出环境参数表
     *
     * @param regionIds 地物ID列表，可选参数
     * @return Excel文件
     */
    @GetMapping("/water-environment")
    public ResponseEntity<Resource> exportWaterEnvironment(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出环境参数表，地物ID列表: {}", regionIds);

        try {
            Resource resource = excelExportService.exportWaterEnvironment(regionIds);

            String contentDisposition = generateFilename(ExcelExportServiceImpl.SHEET_WATER_ENVIRONMENT);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition)
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"))
                    .body(resource);

        } catch (Exception e) {
            log.error("导出环境参数表失败", e);
            throw new RuntimeException("导出环境参数表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出水质参数表
     *
     * @param regionIds 地物ID列表，可选参数
     * @return Excel文件
     */
    @GetMapping("/water-quality")
    public ResponseEntity<Resource> exportWaterQuality(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出水质参数表，地物ID列表: {}", regionIds);

        try {
            Resource resource = excelExportService.exportWaterQuality(regionIds);

            String contentDisposition = generateFilename(ExcelExportServiceImpl.SHEET_WATER_QUALITY);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition)
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"))
                    .body(resource);

        } catch (Exception e) {
            log.error("导出水质参数表失败", e);
            throw new RuntimeException("导出水质参数表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出反射率光谱表
     *
     * @param regionIds 地物ID列表，可选参数
     * @return Excel文件
     */
    @GetMapping("/water-spectrum")
    public ResponseEntity<Resource> exportWaterSpectrum(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出反射率光谱表，地物ID列表: {}", regionIds);

        try {
            Resource resource = excelExportService.exportWaterSpectrum(regionIds);

            String contentDisposition = generateFilename(ExcelExportServiceImpl.SHEET_WATER_SPECTRUM);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition)
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"))
                    .body(resource);

        } catch (Exception e) {
            log.error("导出反射率光谱表失败", e);
            throw new RuntimeException("导出反射率光谱表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出测量记录表
     *
     * @param regionIds 地物ID列表，可选参数
     * @return Excel文件
     */
    @GetMapping("/monitoring-record")
    public ResponseEntity<Resource> exportMonitoringRecord(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出测量记录表，地物ID列表: {}", regionIds);

        try {
            Resource resource = excelExportService.exportMonitoringRecord(regionIds);

            String contentDisposition = generateFilename(ExcelExportServiceImpl.SHEET_MONITORING_RECORD);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition)
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"))
                    .body(resource);

        } catch (Exception e) {
            log.error("导出测量记录表失败", e);
            throw new RuntimeException("导出测量记录表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出影像信息表
     *
     * @param regionIds 地物ID列表，可选参数
     * @return Excel文件
     */
    @GetMapping("/image")
    public ResponseEntity<Resource> exportImage(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出影像信息表，地物ID列表: {}", regionIds);

        try {
            Resource resource = excelExportService.exportImage(regionIds);

            String contentDisposition = generateFilename(ExcelExportServiceImpl.SHEET_IMAGE);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition)
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"))
                    .body(resource);

        } catch (Exception e) {
            log.error("导出影像信息表失败", e);
            throw new RuntimeException("导出影像信息表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成文件名
     * 
     * @param prefix 文件名前缀
     * @return 完整的文件名
     */
    private String generateFilename(String prefix) throws UnsupportedEncodingException {
        String timestamp = LocalDateTime.now().format(FILENAME_FORMATTER);
        String originalFilename = prefix + "_" + timestamp + ".xlsx";

        // 对文件名进行UTF-8编码（转换为ASCII兼容格式）
        String encodedFilename = URLEncoder.encode(originalFilename, StandardCharsets.UTF_8.name());

        // 按照RFC 5987标准设置Content-Disposition，兼容各种浏览器
        String contentDisposition = "attachment; filename=\"" + encodedFilename + "\"; filename*=UTF-8''" + encodedFilename;
        return contentDisposition;
    }
}
