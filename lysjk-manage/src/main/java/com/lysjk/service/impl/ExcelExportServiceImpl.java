package com.lysjk.service.impl;

import com.lysjk.entity.*;
import com.lysjk.service.*;
import com.lysjk.utils.CoordinateUtil;
import com.lysjk.utils.GeometryUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Excel导出服务实现类
 * 实现完整的Excel文件导出功能，支持导出水质监测数据到Excel文件中
 * 导出格式与导入模板基本一致，包含7个工作表
 */
@Slf4j
@Service
public class ExcelExportServiceImpl implements ExcelExportService {

    @Autowired
    private TRegionInfoService regionInfoService;

    @Autowired
    private TMonitoringPointService monitoringPointService;

    @Autowired
    private TWaterEnvironmentService waterEnvironmentService;

    @Autowired
    private TWaterQualityService waterQualityService;

    @Autowired
    private TWaterSpectrumService waterSpectrumService;

    @Autowired
    private TMonitoringRecordService monitoringRecordService;

    @Autowired
    private TImageService imageService;

    // Excel工作表名称常量
    public static final String SHEET_REGION_INFO = "地物信息主表";
    public static final String SHEET_MONITORING_POINT = "监测点信息表";
    public static final String SHEET_WATER_ENVIRONMENT = "环境参数表";
    public static final String SHEET_WATER_QUALITY = "水质参数表";
    public static final String SHEET_WATER_SPECTRUM = "反射率光谱表";
    public static final String SHEET_MONITORING_RECORD = "测量记录表";
    public static final String SHEET_IMAGE = "影像信息表";

    // 日期时间格式化器
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");

    @Override
    public Resource exportCompleteExcel(List<Integer> regionIds) {
        log.info("开始导出完整Excel文件，地物ID列表: {}", regionIds);

        try (Workbook workbook = new SXSSFWorkbook();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ) {
            // 创建所有工作表
            createRegionInfoMainSheet(workbook, regionIds);
            createMonitoringPointSheet(workbook, regionIds);
            createWaterEnvironmentSheet(workbook, regionIds);
            createWaterQualitySheet(workbook, regionIds);
            createWaterSpectrumSheet(workbook, regionIds);
            createMonitoringRecordSheet(workbook, regionIds);
            createImageSheet(workbook, regionIds);

            // 将工作簿写入字节数组
            workbook.write(outputStream);

            log.info("完整Excel文件导出成功，文件大小: {} bytes", outputStream.size());
            // 清理临时文件
            ((SXSSFWorkbook) workbook).dispose();
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出完整Excel文件失败", e);
            throw new RuntimeException("导出Excel文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportRegionInfoMain(List<Integer> regionIds) {
        log.info("开始导出地物信息主表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createRegionInfoMainSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("地物信息主表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出地物信息主表失败", e);
            throw new RuntimeException("导出地物信息主表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportMonitoringPoint(List<Integer> regionIds) {
        log.info("开始导出监测点信息表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createMonitoringPointSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("监测点信息表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出监测点信息表失败", e);
            throw new RuntimeException("导出监测点信息表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportWaterEnvironment(List<Integer> regionIds) {
        log.info("开始导出环境参数表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createWaterEnvironmentSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("环境参数表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出环境参数表失败", e);
            throw new RuntimeException("导出环境参数表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportWaterQuality(List<Integer> regionIds) {
        log.info("开始导出水质参数表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createWaterQualitySheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("水质参数表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出水质参数表失败", e);
            throw new RuntimeException("导出水质参数表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportWaterSpectrum(List<Integer> regionIds) {
        log.info("开始导出反射率光谱表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createWaterSpectrumSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("反射率光谱表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出反射率光谱表失败", e);
            throw new RuntimeException("导出反射率光谱表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportMonitoringRecord(List<Integer> regionIds) {
        log.info("开始导出测量记录表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createMonitoringRecordSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("测量记录表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出测量记录表失败", e);
            throw new RuntimeException("导出测量记录表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportImage(List<Integer> regionIds) {
        log.info("开始导出影像信息表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createImageSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("影像信息表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出影像信息表失败", e);
            throw new RuntimeException("导出影像信息表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建地物信息主表工作表
     */
    private void createRegionInfoMainSheet(Workbook workbook, List<Integer> regionIds) {
        Sheet sheet = workbook.createSheet(SHEET_REGION_INFO);

        // 创建表头（包含更多字段，排除系统字段）
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "地物编码", "地物名称", "省", "市", "区县", "街镇", "坐落位置",
                           "所属河系", "行政编码", "采集时间", "采集方法", "完成单位", "状态", "经度", "纬度", "备注"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            // 设置表头样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            cell.setCellStyle(headerStyle);
        }

        // 查询数据
        List<TRegionInfo> regionInfoList;
        if (regionIds != null && !regionIds.isEmpty()) {
            regionInfoList = regionInfoService.selectByIds(regionIds);
        } else {
            regionInfoList = regionInfoService.selectAll();
        }

        // 填充数据
        int rowIndex = 1;
        for (TRegionInfo regionInfo : regionInfoList) {
            Row row = sheet.createRow(rowIndex++);
            int colIndex = 0;

            // 序号
            setCellValue(row.createCell(colIndex++), regionInfo.getNumber());

            // 地物编码
            setCellValue(row.createCell(colIndex++), regionInfo.getCode());

            // 地物名称
            setCellValue(row.createCell(colIndex++), regionInfo.getName());

            // 省
            setCellValue(row.createCell(colIndex++), regionInfo.getSheng());

            // 市
            setCellValue(row.createCell(colIndex++), regionInfo.getShi());

            // 区县
            setCellValue(row.createCell(colIndex++), regionInfo.getQu());

            // 街镇
            setCellValue(row.createCell(colIndex++), regionInfo.getZhen());

            // 坐落位置
            setCellValue(row.createCell(colIndex++), regionInfo.getZuoluo());

            // 所属河系
            setCellValue(row.createCell(colIndex++), regionInfo.getRiver());

            // 行政编码
            setCellValue(row.createCell(colIndex++), regionInfo.getAdministrativeCode());

            // 采集时间（格式化为日期,从数据读出来的,所以可以格式写在表中,如果LocalDataTime存就不行了）
            if (regionInfo.getSampleDt() != null) {
                setCellValue(row.createCell(colIndex++), regionInfo.getSampleDt().format(DATE_FORMATTER));
            } else {
                setCellValue(row.createCell(colIndex++), "");
            }

            // 采集方法
            setCellValue(row.createCell(colIndex++), regionInfo.getMethod());

            // 完成单位
            setCellValue(row.createCell(colIndex++), regionInfo.getUnit());

            // 状态
            String statusText = "";
            if (regionInfo.getStatus() != null) {
                statusText = regionInfo.getStatus() == 1 ? "包含" : "未包含";
            }
            setCellValue(row.createCell(colIndex++), statusText);

            // 经度和纬度（转换为度分秒格式）
            if (regionInfo.getCenter() != null) {
                Point location = regionInfo.getCenter();
                double longitude = GeometryUtil.getLongitude(location);
                double latitude = GeometryUtil.getLatitude(location);

                // 转换为度分秒格式
                String longitudeDms = CoordinateUtil.decimalToDms(longitude);
                String latitudeDms = CoordinateUtil.decimalToDms(latitude);

                setCellValue(row.createCell(colIndex++), longitudeDms);
                setCellValue(row.createCell(colIndex++), latitudeDms);
            } else {
                setCellValue(row.createCell(colIndex++), "");
                setCellValue(row.createCell(colIndex++), "");
            }

            // 备注
            setCellValue(row.createCell(colIndex++), regionInfo.getRemark());
        }

        // 设置列宽（对于较长的字段设置更宽的列宽）
        sheet.setColumnWidth(0, 2000);  // 序号
        sheet.setColumnWidth(1, 4000);  // 地物编码
        sheet.setColumnWidth(2, 6000);  // 地物名称
        sheet.setColumnWidth(3, 3000);  // 省
        sheet.setColumnWidth(4, 3000);  // 市
        sheet.setColumnWidth(5, 3000);  // 区县
        sheet.setColumnWidth(6, 4000);  // 街镇
        sheet.setColumnWidth(7, 4000);  // 坐落位置
        sheet.setColumnWidth(8, 4000);  // 所属河系
        sheet.setColumnWidth(9, 4000);  // 行政编码
        sheet.setColumnWidth(10, 4000); // 采集时间
        sheet.setColumnWidth(11, 4000); // 采集方法
        sheet.setColumnWidth(12, 6000); // 完成单位
        sheet.setColumnWidth(13, 3000); // 状态
        sheet.setColumnWidth(14, 5000);  // 经度
        sheet.setColumnWidth(15, 5000);  // 纬度
        sheet.setColumnWidth(16, 5000); // 备注

        log.info("地物信息主表创建完成，共导出 {} 条数据", regionInfoList.size());
    }

    /**
     * 创建监测点信息表工作表
     */
    private void createMonitoringPointSheet(Workbook workbook, List<Integer> regionIds) {
        Sheet sheet = workbook.createSheet(SHEET_MONITORING_POINT);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "采样点号", "地点", "经度", "纬度", "备注"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            CellStyle headerStyle = createHeaderStyle(workbook);
            cell.setCellStyle(headerStyle);
        }

        // 查询数据
        List<TMonitoringPoint> monitoringPointList;
        if (regionIds != null && !regionIds.isEmpty()) {
            monitoringPointList = monitoringPointService.selectByRegionIds(regionIds);
        } else {
            monitoringPointList = monitoringPointService.selectAll();
        }

        // 填充数据
        int rowIndex = 1;
        int sequenceNumber = 1;
        for (TMonitoringPoint monitoringPoint : monitoringPointList) {
            Row row = sheet.createRow(rowIndex++);

            // 序号
            setCellValue(row.createCell(0), String.valueOf(sequenceNumber++));

            // 采样点号
            setCellValue(row.createCell(1), monitoringPoint.getCode());

            // 地点
            setCellValue(row.createCell(2), monitoringPoint.getName());

            // 经度和纬度（转换为度分秒格式）
            if (monitoringPoint.getLocation() != null) {
                Point location = monitoringPoint.getLocation();
                double longitude = GeometryUtil.getLongitude(location);
                double latitude = GeometryUtil.getLatitude(location);

                // 转换为度分秒格式
                String longitudeDms = CoordinateUtil.decimalToDms(longitude);
                String latitudeDms = CoordinateUtil.decimalToDms(latitude);

                setCellValue(row.createCell(3), longitudeDms);
                setCellValue(row.createCell(4), latitudeDms);
            } else {
                setCellValue(row.createCell(3), "");
                setCellValue(row.createCell(4), "");
            }

            // 备注
            setCellValue(row.createCell(5), monitoringPoint.getRemark());
        }

        // 调整列宽
        sheet.setColumnWidth(0, 2000);  // 序号
        sheet.setColumnWidth(1, 3000);  // 采样点号
        sheet.setColumnWidth(2, 5000);  // 地点
        sheet.setColumnWidth(3, 5000);  // 经度
        sheet.setColumnWidth(4, 5000);  // 纬度
        sheet.setColumnWidth(5, 5000);  // 备注

        log.info("监测点信息表创建完成，共导出 {} 条数据", monitoringPointList.size());
    }

    /**
     * 创建环境参数表工作表
     */
    private void createWaterEnvironmentSheet(Workbook workbook, List<Integer> regionIds) {
        Sheet sheet = workbook.createSheet(SHEET_WATER_ENVIRONMENT);

        // 创建表头（包含更多字段，排除系统字段）
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "采样点号", "监测日期", "监测时间", "天气", "风向", "风速(m/s)",
                           "气温(℃)", "水温(℃)", "透明度(cm)", "水深(m)", "水面状况", "海拔(m)",
                           "pH值", "溶解氧(mg/L)", "水体浊度", "水体浊度3次记录", "照片文件夹",
                           "反射率光谱", "测量工具和人员", "水体浊度平均值", "溶解氧", "备注"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            CellStyle headerStyle = createHeaderStyle(workbook);
            cell.setCellStyle(headerStyle);
        }

        // 查询数据
        List<TWaterEnvironment> waterEnvironmentList;
        if (regionIds != null && !regionIds.isEmpty()) {
            waterEnvironmentList = waterEnvironmentService.selectByRegionIds(regionIds);
        } else {
            waterEnvironmentList = waterEnvironmentService.selectAll();
        }

        // 填充数据
        int rowIndex = 1;
        int sequenceNumber = 1;
        for (TWaterEnvironment waterEnvironment : waterEnvironmentList) {
            Row row = sheet.createRow(rowIndex++);

            // 序号
            setCellValue(row.createCell(0), String.valueOf(sequenceNumber++));

            // 采样点号（通过监测点ID获取）
            String pointCode = "";
            if (waterEnvironment.getPointId() != null) {
                TMonitoringPoint point = monitoringPointService.selectByPrimaryKey(waterEnvironment.getPointId());
                if (point != null) {
                    pointCode = point.getCode();
                }
            }
            setCellValue(row.createCell(1), pointCode);

            // 监测日期和监测时间（分离显示）
            if (waterEnvironment.getMonitoringDt() != null) {
                LocalDateTime monitoringTime = waterEnvironment.getMonitoringDt();
                setCellValue(row.createCell(2), monitoringTime.format(DATE_FORMATTER));
                setCellValue(row.createCell(3), monitoringTime.format(TIME_FORMATTER));
            } else {
                setCellValue(row.createCell(2), "");
                setCellValue(row.createCell(3), "");
            }

            // 天气
            setCellValue(row.createCell(4), waterEnvironment.getWeather());

            // 风向
            setCellValue(row.createCell(5), waterEnvironment.getWindDirection());

            // 风速
            setCellValue(row.createCell(6), formatDoubleValue(waterEnvironment.getWindSpeed()));

            // 气温
            setCellValue(row.createCell(7), formatDoubleValue(waterEnvironment.getAirTemperature()));

            // 水温
            setCellValue(row.createCell(8), formatDoubleValue(waterEnvironment.getWaterTemperature()));

            // 透明度
            setCellValue(row.createCell(9), formatDoubleValue(waterEnvironment.getWaterTransparency()));

            // 水深
            setCellValue(row.createCell(10), formatDoubleValue(waterEnvironment.getWaterDepth()));

            // 水面状况
            setCellValue(row.createCell(11), waterEnvironment.getSurfaceCondition());

            // 海拔
            setCellValue(row.createCell(12), formatDoubleValue(waterEnvironment.getElevation()));

            // pH值
            setCellValue(row.createCell(13), formatDoubleValue(waterEnvironment.getPh()));

            // 溶解氧
            setCellValue(row.createCell(14), formatDoubleValue(waterEnvironment.getDissolvedOxyg()));

            // 水体浊度
            setCellValue(row.createCell(15), formatDoubleValue(waterEnvironment.getWaterTurbidity()));

            // 水体浊度3次记录
            setCellValue(row.createCell(16), waterEnvironment.getWaterTurbidity3());

            // 照片文件夹
            setCellValue(row.createCell(17), waterEnvironment.getPhotoFolder());

            // 反射率光谱
            setCellValue(row.createCell(18), waterEnvironment.getReflectanceSpectrum());

            // 测量工具和人员（JSON格式转换为可读格式）
            String extensionText = "";
            if (waterEnvironment.getExtension() != null && !waterEnvironment.getExtension().trim().isEmpty()) {
                extensionText = waterEnvironment.getExtension(); // 直接显示JSON，或者可以解析后格式化
            }
            setCellValue(row.createCell(19), extensionText);

            // 水体浊度平均值
            setCellValue(row.createCell(20), formatDoubleValue(waterEnvironment.getWaterTurbidity()));

            // 溶解氧
            setCellValue(row.createCell(21), formatDoubleValue(waterEnvironment.getDissolvedOxyg()));

            // 备注
            setCellValue(row.createCell(22), waterEnvironment.getRemark());
        }

        // 设置列宽（对于较长的字段设置更宽的列宽）
        sheet.setColumnWidth(0, 2000);  // 序号
        sheet.setColumnWidth(1, 3000);  // 采样点号
        sheet.setColumnWidth(2, 3500);  // 监测日期
        sheet.setColumnWidth(3, 3000);  // 监测时间
        sheet.setColumnWidth(4, 3000);  // 天气
        sheet.setColumnWidth(5, 3000);  // 风向
        sheet.setColumnWidth(6, 3500);  // 风速
        sheet.setColumnWidth(7, 3500);  // 气温
        sheet.setColumnWidth(8, 3500);  // 水温
        sheet.setColumnWidth(9, 4000);  // 透明度
        sheet.setColumnWidth(10, 3500); // 水深
        sheet.setColumnWidth(11, 4000); // 水面状况
        sheet.setColumnWidth(12, 3500); // 海拔
        sheet.setColumnWidth(13, 3000); // pH值
        sheet.setColumnWidth(14, 4500); // 溶解氧
        sheet.setColumnWidth(15, 4000); // 水体浊度
        sheet.setColumnWidth(16, 6000); // 水体浊度3次记录
        sheet.setColumnWidth(17, 4000); // 照片文件夹
        sheet.setColumnWidth(18, 5000); // 反射率光谱
        sheet.setColumnWidth(19, 8000); // 测量工具和人员
        sheet.setColumnWidth(20, 4400); // 水体浊度平均值
        sheet.setColumnWidth(21, 3000); // 溶解氧
        sheet.setColumnWidth(22, 5000); // 备注

        log.info("环境参数表创建完成，共导出 {} 条数据", waterEnvironmentList.size());
    }

    /**
     * 创建水质参数表工作表
     */
    private void createWaterQualitySheet(Workbook workbook, List<Integer> regionIds) {
        Sheet sheet = workbook.createSheet(SHEET_WATER_QUALITY);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "采样点号", "监测日期", "监测时间", "pH", "溶解氧(mg/L)", "高锰酸盐指数(mg/L)",
                           "氨氮(mg/L)", "总磷(mg/L)", "总氮(mg/L)", "叶绿素", "有机悬浮物", "无机悬浮物",
                           "总悬浮物", "透明度", "浊度", "水温", "电导率", "亚硝态氮", "硝态氮",
                           "CDOM(400NM)", "CDOM(440NM)", "CDOM(480NM)", "Rrs689/Rrs675", "氟离子",
                           "氯离子", "硫酸根离子", "黄色物质", "备注"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            CellStyle headerStyle = createHeaderStyle(workbook);
            cell.setCellStyle(headerStyle);
        }

        // 查询数据
        List<TWaterQuality> waterQualityList;
        if (regionIds != null && !regionIds.isEmpty()) {
            waterQualityList = waterQualityService.selectByRegionIds(regionIds);
        } else {
            waterQualityList = waterQualityService.selectAll();
        }

        // 填充数据
        int rowIndex = 1;
        int sequenceNumber = 1;
        for (TWaterQuality waterQuality : waterQualityList) {
            Row row = sheet.createRow(rowIndex++);

            // 序号
            setCellValue(row.createCell(0), String.valueOf(sequenceNumber++));

            // 采样点号（通过监测点ID获取）
            String pointCode = "";
            if (waterQuality.getPointId() != null) {
                TMonitoringPoint point = monitoringPointService.selectByPrimaryKey(waterQuality.getPointId());
                if (point != null) {
                    pointCode = point.getCode();
                }
            }
            setCellValue(row.createCell(1), pointCode);

            // 监测日期和监测时间（分离显示）
            if (waterQuality.getMonitoringDt() != null) {
                LocalDateTime monitoringTime = waterQuality.getMonitoringDt();
                setCellValue(row.createCell(2), monitoringTime.format(DATE_FORMATTER));
                setCellValue(row.createCell(3), monitoringTime.format(TIME_FORMATTER));
            } else {
                setCellValue(row.createCell(2), "");
                setCellValue(row.createCell(3), "");
            }

            // 各种水质参数
            setCellValue(row.createCell(4), formatDoubleValue(waterQuality.getPh()));
            setCellValue(row.createCell(5), formatDoubleValue(waterQuality.getDissolvedOxyg()));
            setCellValue(row.createCell(6), formatDoubleValue(waterQuality.getPermanganateIndex()));
            setCellValue(row.createCell(7), formatDoubleValue(waterQuality.getAmmoniaNitrogen()));
            setCellValue(row.createCell(8), formatDoubleValue(waterQuality.getTp()));
            setCellValue(row.createCell(9), formatDoubleValue(waterQuality.getTn()));
            setCellValue(row.createCell(10), formatDoubleValue(waterQuality.getChlorophyll()));
            setCellValue(row.createCell(11), formatDoubleValue(waterQuality.getTssOrganic()));
            setCellValue(row.createCell(12), formatDoubleValue(waterQuality.getTssInorganic()));
            setCellValue(row.createCell(13), formatDoubleValue(waterQuality.getTss()));
            setCellValue(row.createCell(14), formatDoubleValue(waterQuality.getTransparency()));
            setCellValue(row.createCell(15), formatDoubleValue(waterQuality.getTurbidity()));
            setCellValue(row.createCell(16), formatDoubleValue(waterQuality.getWaterTemperature()));
            setCellValue(row.createCell(17), formatDoubleValue(waterQuality.getSpecificConductance()));
            setCellValue(row.createCell(18), formatDoubleValue(waterQuality.getNitriteNitrogen()));
            setCellValue(row.createCell(19), formatDoubleValue(waterQuality.getNitrateNitrogen()));
            setCellValue(row.createCell(20), formatDoubleValue(waterQuality.getCdom400()));
            setCellValue(row.createCell(21), formatDoubleValue(waterQuality.getCdom440()));
            setCellValue(row.createCell(22), formatDoubleValue(waterQuality.getCdom480()));
            setCellValue(row.createCell(23), formatDoubleValue(waterQuality.getRrs689675()));
            setCellValue(row.createCell(24), formatDoubleValue(waterQuality.getFluorinion()));
            setCellValue(row.createCell(25), formatDoubleValue(waterQuality.getChloridion()));
            setCellValue(row.createCell(26), formatDoubleValue(waterQuality.getSulfateIon()));
            setCellValue(row.createCell(27), formatDoubleValue(waterQuality.getYellowSubstance()));

            // 备注
            setCellValue(row.createCell(28), waterQuality.getRemark());
        }

        // 调整列宽
        sheet.setColumnWidth(0, 2000);  // 序号
        sheet.setColumnWidth(1, 3000);  // 采样点号
        sheet.setColumnWidth(2, 3500);  // 监测日期
        sheet.setColumnWidth(3, 3000);  // 监测时间
        sheet.setColumnWidth(4, 3000);  // pH
        sheet.setColumnWidth(5, 4500);  // 溶解氧
        sheet.setColumnWidth(6, 5000);  // 高锰酸盐指数
        sheet.setColumnWidth(7, 3500);  // 氨氮
        sheet.setColumnWidth(8, 3000);  // 总磷
        sheet.setColumnWidth(9, 3000);  // 总氮
        sheet.setColumnWidth(10, 3500); // 叶绿素
        sheet.setColumnWidth(11, 4000); // 有机悬浮物
        sheet.setColumnWidth(12, 4000); // 无机悬浮物
        sheet.setColumnWidth(13, 4000); // 总悬浮物
        sheet.setColumnWidth(14, 4000); // 透明度
        sheet.setColumnWidth(15, 3500);  // 浊度
        sheet.setColumnWidth(16, 3500);  // 水温
        sheet.setColumnWidth(17, 4000);  // 电导率
        sheet.setColumnWidth(18, 4000);  // 亚硝态氮
        sheet.setColumnWidth(19, 3500);  // 硝态氮
        sheet.setColumnWidth(20, 4000);  // CDOM(400NM)
        sheet.setColumnWidth(21, 4000);  // CDOM(440NM)
        sheet.setColumnWidth(22, 4000);  // CDOM(480NM)
        sheet.setColumnWidth(23, 4000);  // Rrs689/Rrs675
        sheet.setColumnWidth(24, 3500);  // 氟离子
        sheet.setColumnWidth(25, 3500);  // 氯离子
        sheet.setColumnWidth(26, 4000);  // 硫酸根离子
        sheet.setColumnWidth(27, 3500);  // 黄色物质
        sheet.setColumnWidth(28, 5000);  // 备注


        log.info("水质参数表创建完成，共导出 {} 条数据", waterQualityList.size());
    }

    /**
     * 创建反射率光谱表工作表（按照导入格式设计）
     * 第一行：序号 1 2 3 4 ...
     * 第二行：采样点号 LH01 LH02 LH03 ...
     * 第三行开始：波长 400 401 402 ... 对应的光谱值
     */
    private void createWaterSpectrumSheet(Workbook workbook, List<Integer> regionIds) {
        Sheet sheet = workbook.createSheet(SHEET_WATER_SPECTRUM);

        // 查询数据
        List<TWaterSpectrum> waterSpectrumList;
        if (regionIds != null && !regionIds.isEmpty()) {
            waterSpectrumList = waterSpectrumService.selectByRegionIds(regionIds);
        } else {
            waterSpectrumList = waterSpectrumService.selectAll();
        }

        if (waterSpectrumList.isEmpty()) {
            // 如果没有数据，创建基本表头
            Row headerRow1 = sheet.createRow(0);
            Row headerRow2 = sheet.createRow(1);

            Cell cell1 = headerRow1.createCell(0);
            cell1.setCellValue("序号");
            cell1.setCellStyle(createHeaderStyle(workbook));

            Cell cell2 = headerRow2.createCell(0);
            cell2.setCellValue("采样点号");
            cell2.setCellStyle(createHeaderStyle(workbook));

            log.info("反射率光谱表创建完成，无数据");
            return;
        }

        // 收集所有采样点号和对应的光谱数据
        Map<String, Map<String, Double>> pointSpectrumMap = new LinkedHashMap<>();
        Set<String> allWavelengths = new TreeSet<>((a, b) -> {
            try {
                return Integer.compare(Integer.parseInt(a), Integer.parseInt(b));
            } catch (NumberFormatException e) {
                return a.compareTo(b);
            }
        });

        for (TWaterSpectrum waterSpectrum : waterSpectrumList) {
            // 获取采样点号
            String pointCode = "";
            if (waterSpectrum.getPointId() != null) {
                TMonitoringPoint point = monitoringPointService.selectByPrimaryKey(waterSpectrum.getPointId());
                if (point != null) {
                    pointCode = point.getCode();
                }
            }

            if (!pointCode.isEmpty() && waterSpectrum.getSpectrum() != null) {
                pointSpectrumMap.put(pointCode, waterSpectrum.getSpectrum());
                allWavelengths.addAll(waterSpectrum.getSpectrum().keySet());
            }
        }

        if (pointSpectrumMap.isEmpty()) {
            log.warn("反射率光谱表无有效数据");
            return;
        }

        // 创建第一行：序号 1 2 3 4 ...
        Row row1 = sheet.createRow(0);
        Cell cell1_0 = row1.createCell(0);
        cell1_0.setCellValue("序号");
        cell1_0.setCellStyle(createHeaderStyle(workbook));

        int colIndex = 1;
        for (int i = 1; i <= pointSpectrumMap.size(); i++) {
            Cell cell = row1.createCell(colIndex++);
            cell.setCellValue(String.valueOf(i));
            cell.setCellStyle(createHeaderStyle(workbook));
        }

        // 创建第二行：采样点号 LH01 LH02 LH03 ...
        Row row2 = sheet.createRow(1);
        Cell cell2_0 = row2.createCell(0);
        cell2_0.setCellValue("采样点号");
        cell2_0.setCellStyle(createHeaderStyle(workbook));

        colIndex = 1;
        for (String pointCode : pointSpectrumMap.keySet()) {
            Cell cell = row2.createCell(colIndex++);
            cell.setCellValue(pointCode);
            cell.setCellStyle(createHeaderStyle(workbook));
        }

        // 从第三行开始：波长和对应的光谱值
        int rowIndex = 2;
        for (String wavelength : allWavelengths) {
            Row row = sheet.createRow(rowIndex++);

            // 第一列：波长
            Cell wavelengthCell = row.createCell(0);
            wavelengthCell.setCellValue(wavelength);

            // 后续列：各采样点在该波长下的光谱值
            colIndex = 1;
            for (String pointCode : pointSpectrumMap.keySet()) {
                Map<String, Double> spectrumData = pointSpectrumMap.get(pointCode);
                Double value = spectrumData.get(wavelength);

                Cell cell = row.createCell(colIndex++);
                if (value != null) {
                    cell.setCellValue(value);
                } else {
                    cell.setCellValue("");
                }
            }
        }

        // 设置列宽
        sheet.setColumnWidth(0, 3000); // 波长列稍宽一些
        for (int i = 1; i <= pointSpectrumMap.size(); i++) {
            sheet.setColumnWidth(i, 4000); // 数据列
        }

        log.info("反射率光谱表创建完成，共导出 {} 个采样点，{} 个波长",
                pointSpectrumMap.size(), allWavelengths.size());
    }

    /**
     * 创建测量记录表工作表
     */
    private void createMonitoringRecordSheet(Workbook workbook, List<Integer> regionIds) {
        Sheet sheet = workbook.createSheet(SHEET_MONITORING_RECORD);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "采样点号", "点位测量记录", "风向风速", "光谱测量", "透明度测量",
                           "浊度测量", "深度测量", "水色照片", "实验室检测", "备注"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            CellStyle headerStyle = createHeaderStyle(workbook);
            cell.setCellStyle(headerStyle);
        }

        // 查询数据
        List<TMonitoringRecord> monitoringRecordList;
        if (regionIds != null && !regionIds.isEmpty()) {
            monitoringRecordList = monitoringRecordService.selectByRegionIds(regionIds);
        } else {
            monitoringRecordList = monitoringRecordService.selectAll();
        }

        // 填充数据
        int rowIndex = 1;
        int sequenceNumber = 1;
        for (TMonitoringRecord monitoringRecord : monitoringRecordList) {
            Row row = sheet.createRow(rowIndex++);

            // 序号
            setCellValue(row.createCell(0), String.valueOf(sequenceNumber++));

            // 采样点号（通过监测点ID获取）
            String pointCode = "";
            if (monitoringRecord.getPointId() != null) {
                TMonitoringPoint point = monitoringPointService.selectByPrimaryKey(monitoringRecord.getPointId());
                if (point != null) {
                    pointCode = point.getCode();
                }
            }
            setCellValue(row.createCell(1), pointCode);

            // 点位测量记录
            setCellValue(row.createCell(2), monitoringRecord.getPointMeasure());

            // 风向风速
            setCellValue(row.createCell(3), monitoringRecord.getWindDirectionSpeed());

            // 光谱测量
            setCellValue(row.createCell(4), monitoringRecord.getSpectralMeasure());

            // 透明度测量
            setCellValue(row.createCell(5), monitoringRecord.getTransparencyMeasure());

            // 浊度测量
            setCellValue(row.createCell(6), monitoringRecord.getTurbidityMeasure());

            // 深度测量
            setCellValue(row.createCell(7), monitoringRecord.getDepthMeasure());

            // 水色照片
            setCellValue(row.createCell(8), monitoringRecord.getWaterColorPhoto());

            // 实验室检测
            setCellValue(row.createCell(9), monitoringRecord.getLaboratoryTest());

            // 备注
            setCellValue(row.createCell(10), monitoringRecord.getRemark());
        }

        // 调整测量记录表列宽
        sheet.setColumnWidth(0, 2000);  // 序号
        sheet.setColumnWidth(1, 3000);  // 采样点号
        sheet.setColumnWidth(2, 5000);  // 点位测量记录
        sheet.setColumnWidth(3, 3000);  // 风向风速
        sheet.setColumnWidth(4, 3000);  // 光谱测量
        sheet.setColumnWidth(5, 3000);  // 透明度测量
        sheet.setColumnWidth(6, 3000);  // 浊度测量
        sheet.setColumnWidth(7, 3000);  // 深度测量
        sheet.setColumnWidth(8, 3000);  // 水色照片
        sheet.setColumnWidth(9, 3000);  // 实验室检测
        sheet.setColumnWidth(10, 5000); // 备注

        log.info("测量记录表创建完成，共导出 {} 条数据", monitoringRecordList.size());
    }

    /**
     * 创建影像信息表工作表
     */
    private void createImageSheet(Workbook workbook, List<Integer> regionIds) {
        Sheet sheet = workbook.createSheet(SHEET_IMAGE);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "影像名称", "影像获取时间", "传感器类型", "存储路径",
                           "元数据", "备注"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            CellStyle headerStyle = createHeaderStyle(workbook);
            cell.setCellStyle(headerStyle);
        }

        // 查询数据
        List<TImage> imageList;
        if (regionIds != null && !regionIds.isEmpty()) {
            imageList = imageService.selectByRegionIds(regionIds);
        } else {
            imageList = imageService.selectAll();
        }

        // 填充数据
        int rowIndex = 1;
        int sequenceNumber = 1;
        for (TImage image : imageList) {
            Row row = sheet.createRow(rowIndex++);

            // 序号
            setCellValue(row.createCell(0), String.valueOf(sequenceNumber++));

            // 影像名称
            setCellValue(row.createCell(1), image.getName());

            // 影像获取时间（完整的日期时间格式）
            if (image.getAcquisitionDt() != null) {
                setCellValue(row.createCell(2), image.getAcquisitionDt().format(DATE_FORMATTER));
            } else {
                setCellValue(row.createCell(2), "");
            }

            // 传感器类型
            setCellValue(row.createCell(3), image.getSensorType());

            // 存储路径
            setCellValue(row.createCell(4), image.getFilePath());

            // 元数据
            setCellValue(row.createCell(5), image.getMetaData());

            // 备注
            setCellValue(row.createCell(6), image.getRemark());
        }

        // 调整影像信息表列宽
        sheet.setColumnWidth(0, 2000);  // 序号
        sheet.setColumnWidth(1, 3000);  // 影像名称
        sheet.setColumnWidth(2, 4000);  // 影像获取时间
        sheet.setColumnWidth(3, 3000);  // 传感器类型
        sheet.setColumnWidth(4, 5000);  // 存储路径
        sheet.setColumnWidth(5, 5000);  // 元数据
        sheet.setColumnWidth(6, 5000);  // 备注

        log.info("影像信息表创建完成，共导出 {} 条数据", imageList.size());
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置背景色
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置字体
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        return style;
    }

    /**
     * 设置单元格值（处理null值）
     */
    private void setCellValue(Cell cell, String value) {
        if (value != null && !value.trim().isEmpty()) {
            cell.setCellValue(value);
        } else {
            cell.setCellValue("");
        }
    }

    /**
     * 格式化Double值
     */
    private String formatDoubleValue(Double value) {
        if (value == null) {
            return "";
        }
        // 保留适当的小数位数，去除不必要的零
        if (value == value.intValue()) {
            return String.valueOf(value.intValue());
        } else {
            return String.format("%.6f", value).replaceAll("0+$", "").replaceAll("\\.$", "");
        }
    }

    /**
     * 从Map解析光谱数据的波长列
     */
    private String[] parseWavelengthColumnsFromMap(Map<String, Double> spectrumMap) {
        if (spectrumMap == null || spectrumMap.isEmpty()) {
            return new String[0];
        }

        try {
            return spectrumMap.keySet().stream()
                    .sorted((a, b) -> {
                        try {
                            return Integer.compare(Integer.parseInt(a), Integer.parseInt(b));
                        } catch (NumberFormatException e) {
                            return a.compareTo(b);
                        }
                    })
                    .toArray(String[]::new);
        } catch (Exception e) {
            log.warn("解析光谱数据波长列失败: {}", e.getMessage());
            return new String[0];
        }
    }

    /**
     * 解析光谱数据的波长列（兼容String格式）
     */
    private String[] parseWavelengthColumns(String spectrumJson) {
        if (spectrumJson == null || spectrumJson.trim().isEmpty()) {
            return new String[0];
        }

        try {
            // 简单的JSON解析，提取波长键
            // 假设JSON格式为 {"400": 0.123, "450": 0.234, ...}
            Map<String, Double> spectrumData = parseSpectrumData(spectrumJson);
            return spectrumData.keySet().stream()
                    .sorted((a, b) -> Integer.compare(Integer.parseInt(a), Integer.parseInt(b)))
                    .toArray(String[]::new);
        } catch (Exception e) {
            log.warn("解析光谱数据波长列失败: {}", e.getMessage());
            return new String[0];
        }
    }

    /**
     * 解析光谱数据
     */
    private Map<String, Double> parseSpectrumData(String spectrumJson) {
        Map<String, Double> result = new java.util.HashMap<>();

        if (spectrumJson == null || spectrumJson.trim().isEmpty()) {
            return result;
        }

        try {
            // 简单的JSON解析实现
            // 移除大括号和空格
            String content = spectrumJson.trim().replaceAll("[{}\\s]", "");
            if (content.isEmpty()) {
                return result;
            }

            // 按逗号分割键值对
            String[] pairs = content.split(",");
            for (String pair : pairs) {
                String[] keyValue = pair.split(":");
                if (keyValue.length == 2) {
                    String key = keyValue[0].replaceAll("\"", "").trim();
                    String value = keyValue[1].replaceAll("\"", "").trim();
                    try {
                        result.put(key, Double.parseDouble(value));
                    } catch (NumberFormatException e) {
                        log.warn("解析光谱数据值失败: {} = {}", key, value);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析光谱数据失败: {}", e.getMessage());
        }

        return result;
    }
}
