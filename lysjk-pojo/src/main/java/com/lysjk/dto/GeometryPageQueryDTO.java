package com.lysjk.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 根据地理坐标几何查询(可用作公共的类字段)
 */
@Data
public class GeometryPageQueryDTO implements Serializable {
    //页码(先默认设值)
    private int page = 1;

    //每页显示记录数
    private int pageSize = 10;

    // 经度
    double longitude;

    // 纬度
    double latitude;

    // 距离
    double distance;

    private static final long serialVersionUID = 1L;
}
