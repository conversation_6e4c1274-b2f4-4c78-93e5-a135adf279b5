package com.lysjk.dto.excel;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * excel水质参数表
 * 对应t_water_quality
 */
@Data
public class TWaterQualityExcelDTO implements Serializable {
    /**
     * 水质参数表主键ID自增
     */
    private Integer id;

    /**
     * 采样点号
     * 注意该字段是excel中写得(数据库并没有存储),用来填写关联的pointId和regionId
     */
    private String code;

    /**
     * 监测点ID 通过code关联查询监测点表
     */
    private Integer pointId;

    /**
     * 所属地物ID 通过code关联查询监测点表
     */
    private Integer regionId;

    /**
     * 监测时间
     */
    private LocalDateTime monitoringDt;

    /**
     * 透明度
     */
    private Double transparency;

    /**
     * 浊度
     */
    private Double turbidity;

    /**
     * 总悬浮物
     */
    private Double tss;

    /**
     * 高锰酸盐指数
     */
    private Double permanganateIndex;

    /**
     * 总磷
     */
    private Double tp;

    /**
     * 总氮
     */
    private Double tn;

    /**
     * 氨氮
     */
    private Double ammoniaNitrogen;

    /**
     * 黄色物质,新添加的字段
     */
    private Double yellowSubstance;

    /**
     * 叶绿素a
     */
    private Double chlorophyll;

    private static final long serialVersionUID = 1L;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDt;

    /**
     * 记录创建者ID
     */
    private Integer createBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDt;

    /**
     * 记录修改者ID
     */
    private Integer updateBy;
}