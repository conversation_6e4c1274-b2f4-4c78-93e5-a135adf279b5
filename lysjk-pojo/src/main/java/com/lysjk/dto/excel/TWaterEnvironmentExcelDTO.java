package com.lysjk.dto.excel;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * excel环境参数表
 * 对应t_water_environment
 */
@Data
public class TWaterEnvironmentExcelDTO implements Serializable {

    /**
     * 水域环境表ID主键自增
     */
    private Integer id;

    /**
     * 采样点号
     * 注意该字段是excel中写得(数据库并没有存储),用来填写关联的pointId和regionId
     */
    private String code;

    /**
     * 监测点ID 通过code关联查询监测点表
     */
    private Integer pointId;

    /**
     * 所属地物ID 通过code关联查询监测点表
     */
    private Integer regionId;

    /**
     * 监测时间
     * 注意该字段需要excel中的日期和时间来一块拼接才能保存到数据库
     */
    private LocalDateTime monitoringDt;

    /**
     * 风速
     */
    private Double windSpeed;

    /**
     * 风向
     */
    private String windDirection;

    /**
     * 海拔
     */
    private Double elevation;

    /**
     * 气温
     */
    private Double airTemperature;

    /**
     * 水温
     */
    private Double waterTemperature;

    /**
     * ph
     */
    private Double ph;

    /**
     * 溶解氧
     */
    private Double dissolvedOxyg;

    /**
     * 水深度
     */
    private Double waterDepth;

    /**
     * 水面状况
     */
    private String surfaceCondition;


    /**
     * 天气状况
     */
    private String weather;

    /**
     * 照片序号
     */
    private String photoFolder;

    /**
     * 反射率光谱
     */
    private String reflectanceSpectrum;

    /**
     * 备注
     */
    private String remark;

    private static final long serialVersionUID = 1L;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDt;

    /**
     * 记录创建者ID
     */
    private Integer createBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDt;

    /**
     * 记录修改者ID
     */
    private Integer updateBy;
}
